import fs from 'fs';
import sharp from 'sharp';

// 输入和输出文件路径
const inputPath = 'public/assets/img/<EMAIL>';
const outputPath = 'public/assets/img/heatmap-preview.jpg';

// 压缩选项
const options = {
  quality: 80, // JPEG质量 (0-100)
  width: 1200, // 最大宽度
};

// 检查输入文件是否存在
if (!fs.existsSync(inputPath)) {
  console.error(`输入文件不存在: ${inputPath}`);
  process.exit(1);
}

// 确保输出目录存在
const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// 压缩图片
sharp(inputPath)
  .resize(options.width) // 调整大小，保持宽高比
  .jpeg({ quality: options.quality }) // 转换为JPEG并设置质量
  .toFile(outputPath)
  .then(info => {
    console.log(`图片已成功压缩并保存到 ${outputPath}`);
    console.log(`原始大小: ${fs.statSync(inputPath).size / 1024} KB`);
    console.log(`压缩后大小: ${info.size / 1024} KB`);
    console.log(`压缩率: ${(1 - info.size / fs.statSync(inputPath).size) * 100}%`);
  })
  .catch(err => {
    console.error('压缩图片时出错:', err);
  });
