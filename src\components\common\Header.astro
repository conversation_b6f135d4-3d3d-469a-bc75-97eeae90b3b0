---
import ThemeToggle from './ThemeToggle.astro';
---

<header class="fixed top-0 left-0 w-full bg-white dark:bg-[#121417] border-b border-gray-200 dark:border-[#27292e] z-50 transition-colors duration-300">
  <progress id="progress" value="0" max="100" class="w-full h-1 appearance-none fixed top-0 left-0 z-50">
    <div class="progress-container">
      <span class="progress-bar"></span>
    </div>
  </progress>
  <nav id="navbar" class="container mx-auto px-4 py-3 max-w-[770px]">
    <div class="flex justify-between items-center">
      <button class="md:hidden navbar-toggler relative w-8 h-8 flex justify-center items-center cursor-pointer hover:opacity-80" type="button" aria-label="Toggle navigation">
        <span class="sr-only">切换导航</span>
        <div class="hamburger-icon w-6 h-8 relative">
          <span class="hamburger-bar absolute w-full h-[2px] bg-gray-700 dark:bg-gray-300 transition-all duration-300" style="top: 25%"></span>
          <span class="hamburger-bar absolute w-full h-[2px] bg-gray-700 dark:bg-gray-300 transition-all duration-300" style="top: 50%"></span>
          <span class="hamburger-bar absolute w-full h-[2px] bg-gray-700 dark:bg-gray-300 transition-all duration-300" style="top: 74%"></span>
        </div>
      </button>

      <div class="hidden md:flex md:items-center md:space-x-6 navbar-collapse">
        <a href="/" class="nav-link text-gray-800 dark:text-[#e2e8f0] font-medium hover:text-blue-600 dark:hover:text-[#3b82f6]">About</a>
        <a href="/publications/" class="nav-link text-blue-500 dark:text-[#9ca3af] font-medium hover:text-gray-800 dark:hover:text-[#e2e8f0]">Publications</a>
        <a href="/software/" class="nav-link text-blue-500 dark:text-[#9ca3af] font-medium hover:text-gray-800 dark:hover:text-[#e2e8f0]">Software</a>
        <a href="/photography/" class="nav-link text-blue-500 dark:text-[#9ca3af] font-medium hover:text-gray-800 dark:hover:text-[#e2e8f0]">Photography</a>
      </div>

      <ThemeToggle />
    </div>

    <!-- 移动端菜单 -->
    <div class="mobile-menu hidden md:hidden mt-2 py-2 space-y-2">
      <a href="/" class="block py-2 px-4 text-gray-800 dark:text-[#e2e8f0] hover:bg-gray-100 dark:hover:bg-[#18191d]">About</a>
      <a href="/publications/" class="block py-2 px-4 text-blue-500 dark:text-[#9ca3af] hover:bg-gray-100 dark:hover:bg-[#18191d]">Publications</a>
      <a href="/software/" class="block py-2 px-4 text-blue-500 dark:text-[#9ca3af] hover:bg-gray-100 dark:hover:bg-[#18191d]">Software</a>
      <a href="/photography/" class="block py-2 px-4 text-blue-500 dark:text-[#9ca3af] hover:bg-gray-100 dark:hover:bg-[#18191d]">Photography</a>
    </div>
  </nav>
</header>

<script>
  // 移动端菜单切换
  const navbarToggler = document.querySelector('.navbar-toggler');
  const mobileMenu = document.querySelector('.mobile-menu');

  if (navbarToggler && mobileMenu) {
    navbarToggler.addEventListener('click', () => {
      mobileMenu.classList.toggle('hidden');
      navbarToggler.classList.toggle('active');
    });
  }

  // 高亮当前页面的导航链接
  document.addEventListener('DOMContentLoaded', () => {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
      const href = link.getAttribute('href');
      if (href && (currentPath === href || (href !== '/' && currentPath.startsWith(href)))) {
        link.classList.add('active');
      }
    });
  });
</script>

<style>
  /* 添加一些无法用Tailwind直接实现的样式 */
  .nav-link.active {
    font-weight: 600;
  }

  /* 汉堡图标动画 */
  .hamburger-bar {
    transform-origin: center;
    margin-top: -1px; /* 微调使所有横杠高度一致 */
  }

  .navbar-toggler.active .hamburger-bar:nth-child(1) {
    top: 50% !important;
    transform: rotate(45deg);
  }

  .navbar-toggler.active .hamburger-bar:nth-child(2) {
    opacity: 0;
  }

  .navbar-toggler.active .hamburger-bar:nth-child(3) {
    top: 50% !important;
    transform: rotate(-45deg);
  }

  /* 进度条样式 */
  progress {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    height: 3px;
    background-color: transparent;
    color: #3b82f6;
  }

  progress::-webkit-progress-bar {
    background-color: transparent;
  }

  progress::-webkit-progress-value {
    background-color: #3b82f6;
  }

  progress::-moz-progress-bar {
    background-color: #3b82f6;
  }

  .dark progress::-webkit-progress-value {
    background-color: #60a5fa;
  }

  .dark progress::-moz-progress-bar {
    background-color: #60a5fa;
  }
</style>

<script>
  // 进度条功能
  document.addEventListener('DOMContentLoaded', () => {
    const progressBar = document.getElementById('progress') as HTMLProgressElement;

    if (progressBar) {
      function updateProgressBar() {
        const scrollPosition = window.scrollY;
        const totalHeight = document.body.scrollHeight - window.innerHeight;
        const progress = (scrollPosition / totalHeight) * 100;
        progressBar.value = progress;
      }

      window.addEventListener('scroll', updateProgressBar);
      window.addEventListener('resize', updateProgressBar);

      // 初始化进度条
      updateProgressBar();
    }
  });
</script>
