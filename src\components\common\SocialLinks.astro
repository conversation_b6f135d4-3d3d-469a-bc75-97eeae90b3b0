---
---

<div class="social mt-6 mb-0">
  <div class="contact-icons flex justify-center gap-6">
    <a href="mailto:<EMAIL>" title="Email" class="social-icon-link">
      <i class="fa-solid fa-envelope"></i>
    </a>
    <a href="https://orcid.org/0009-0005-4927-5069" title="ORCID" target="_blank" class="social-icon-link">
      <i class="ai ai-orcid"></i>
    </a>
    <a href="https://github.com/harterz" title="GitHub" target="_blank" class="social-icon-link">
      <i class="fa-brands fa-github"></i>
    </a>
    <a href="https://500px.com.cn/kinyuchu" title="500px Photography Portfolio" target="_blank" class="social-icon-link">
      <i class="fa-solid fa-camera"></i>
    </a>
  </div>
</div>

<style>
  /* 添加Academicons字体支持 */
  @import url('https://cdn.jsdelivr.net/gh/jpswalsh/academicons@1/css/academicons.min.css');

  /* 社交图标样式 */
  .social {
    position: relative;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding-top: 2rem;
    padding-bottom: 0;
  }

  .dark .social {
    border-top: 1px solid rgba(255, 255, 255, 0.05);
  }

  .social .contact-icons {
    font-size: 1.8rem;
  }

  .social .contact-icons a {
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    transition: all 0.3s ease;
    width: 62px;
    height: 50px;
    opacity: 0.8;
  }

  .social .contact-icons a:hover {
    transform: translateY(-3px);
    opacity: 1;
  }

  /* 图标大小调整 */
  .ai, .fa-solid, .fa-brands {
    font-size: 4rem;
  }

  /* 社交图标链接样式 - 使用高特异性选择器和!important确保覆盖全局样式 */
  .social .contact-icons .social-icon-link {
    color: #3b82f6 !important; /* 亮色主题下使用主题蓝色 */
    border-bottom: none !important;
    transition: all 0.3s ease;
  }

  .dark .social .contact-icons .social-icon-link {
    color: rgb(255, 255, 255) !important; /* 暗色主题下使用白色图标 */
  }

  .social .contact-icons .social-icon-link:hover {
    color: #2563eb !important; /* 亮色主题下悬停时的颜色（深蓝色） */
    border-bottom: none !important;
    transform: translateY(-3px);
  }

  .dark .social .contact-icons .social-icon-link:hover {
    color: #e5e7eb !important; /* 暗色主题下悬停时的颜色（浅灰色） */
    border-bottom: none !important;
    transform: translateY(-3px);
  }


</style>
