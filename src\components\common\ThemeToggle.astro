---
---

<button id="theme-toggle" class="theme-toggle-btn" aria-label="切换暗黑模式">
  <svg xmlns="http://www.w3.org/2000/svg" class="theme-icon sun" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <circle cx="12" cy="12" r="5"></circle>
    <line x1="12" y1="1" x2="12" y2="3"></line>
    <line x1="12" y1="21" x2="12" y2="23"></line>
    <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
    <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
    <line x1="1" y1="12" x2="3" y2="12"></line>
    <line x1="21" y1="12" x2="23" y2="12"></line>
    <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
    <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
  </svg>
  <svg xmlns="http://www.w3.org/2000/svg" class="theme-icon moon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
  </svg>
</button>

<script>
  // 主题切换逻辑
  const themeToggle = document.getElementById('theme-toggle');
  const sunIcon = document.querySelector('.theme-icon.sun');
  const moonIcon = document.querySelector('.theme-icon.moon');

  // 更新图标显示状态
  function updateIconVisibility(isDark) {
    if (sunIcon && moonIcon) {
      if (isDark) {
        sunIcon.classList.add('hidden');
        moonIcon.classList.remove('hidden');
      } else {
        sunIcon.classList.remove('hidden');
        moonIcon.classList.add('hidden');
      }
    }
  }

  // 检查本地存储中的主题偏好
  const isDarkMode = localStorage.getItem('theme') === 'dark' ||
      (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches);

  if (isDarkMode) {
    document.documentElement.classList.add('dark');
  } else {
    document.documentElement.classList.remove('dark');
  }

  // 初始化图标状态
  updateIconVisibility(isDarkMode);

  // 添加点击事件监听器
  if (themeToggle) {
    themeToggle.addEventListener('click', () => {
      // 切换暗色模式
      const isDark = document.documentElement.classList.contains('dark');
      if (isDark) {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('theme', 'light');
      } else {
        document.documentElement.classList.add('dark');
        localStorage.setItem('theme', 'dark');
      }
      localStorage.setItem('theme_manual_set', 'true');

      // 更新图标
      updateIconVisibility(!isDark);
    });
  }

  // 监听系统主题变化（仅当用户未手动设置主题时）
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
    if (!localStorage.getItem('theme_manual_set')) {
      const isDark = e.matches;
      if (isDark) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }

      // 更新图标
      updateIconVisibility(isDark);
    }
  });
</script>

<style>
  .theme-toggle-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
    width: 36px;
    height: 36px;
  }

  .theme-toggle-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .dark .theme-toggle-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  /* 主题图标 */
  .theme-icon {
    width: 20px;
    height: 20px;
    transition: all 0.3s ease;
    stroke-width: 2px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .theme-icon.sun {
    stroke: #f39c12;
    opacity: 1;
  }

  .theme-icon.moon {
    stroke: #60a5fa;
    opacity: 1;
  }

  /* 黑暗模式下的样式 */
  .dark .theme-icon.sun {
    stroke: #f1c40f;
  }

  .dark .theme-icon.moon {
    stroke: #60a5fa;
  }

  /* 隐藏类 */
  .hidden {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }

  /* 响应式调整 */
  @media (max-width: 640px) {
    .theme-toggle-btn {
      width: 32px;
      height: 32px;
      padding: 6px;
    }

    .theme-icon {
      width: 18px;
      height: 18px;
    }
  }
</style>
