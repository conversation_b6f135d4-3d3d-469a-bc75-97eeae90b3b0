---
---

<div id="chart-container" class="relative h-[45vh] overflow-hidden mx-auto max-w-[1000px] p-0"></div>

<script is:inline>
  // 使用全局ECharts对象
  document.addEventListener('DOMContentLoaded', function() {
    // 检查图表容器是否存在
    if (document.getElementById('chart-container')) {
      // 确定当前主题
      function determineComputedTheme() {
        return document.documentElement.classList.contains('dark') ? 'dark' : 'light';
      }

      // 更新ECharts主题
      function updateEChartsTheme() {
        const darkMode = determineComputedTheme() === 'dark';
        const textColor = darkMode ? '#e8e8e8' : '#000000';
        const bgColor = darkMode ? '#1c1c1c' : '#ffffff';

        if (myChart) {
          myChart.setOption({
            darkMode: darkMode,
            backgroundColor: 'transparent',
            title: {
              textStyle: {
                color: textColor
              }
            },
            series: [{
              itemStyle: {
                borderColor: bgColor
              },
              label: {
                color: textColor
              },
              lineStyle: {
                opacity: darkMode ? 0.4 : 0.3 // 黑暗模式下增加不透明度
              }
            }]
          });
        }
      }

      // 检测是否为移动设备
      const isMobile = window.innerWidth <= 600;

      // 初始化ECharts实例
      var dom = document.getElementById('chart-container');
      var myChart = echarts.init(dom, null, {
        renderer: 'canvas',
        useDirtyRect: false
      });

      // 获取当前主题
      let theme = determineComputedTheme();

      // 图表配置
      var option = {
        darkMode: theme == 'dark',
        backgroundColor: 'transparent',
        // 移除标题，因为我们已经在HTML中添加了标题
        tooltip: {
          trigger: 'item',
          triggerOn: 'mousemove',
          backgroundColor: theme == 'dark' ? 'rgba(50, 50, 50, 0.9)' : 'rgba(255, 255, 255, 0.9)',
          borderColor: theme == 'dark' ? 'rgba(100, 100, 100, 0.9)' : 'rgba(200, 200, 200, 0.9)',
          textStyle: {
            color: theme == 'dark' ? '#e8e8e8' : '#000000'
          }
        },
        series: {
          type: 'sankey',
          layout: 'none',
          nodeWidth: isMobile ? 5 : 15, // 减小节点宽度，使图表更加精致
          nodeGap: isMobile ? 3 : 8, // 增加节点间距，使布局更加通透
          nodeAlign: 'justify', // 使用justify对齐，在保持节点高度一致的同时更加平衡
          layoutIterations: 100, // 增加布局迭代次数，使节点分布更加均匀
          animation: true, // 启用动画
          animationDuration: 1000, // 设置动画持续时间
          animationEasing: 'cubicOut', // 使用平滑的缓动函数
          emphasis: {
            focus: 'trajectory', // 高亮相邻节点
            itemStyle: {
              borderWidth: 1,
              borderColor: theme == 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.2)',
              shadowBlur: 5,
              shadowColor: theme == 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)'
            },
            lineStyle: {
              opacity: theme == 'dark' ? 0.8 : 0.6,
              shadowBlur: 5,
              shadowColor: theme == 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)'
            },
            label: {
              fontWeight: 'bold'
            }
          },
          label: {
            fontSize: isMobile ? 10 : 12,
            color: theme == 'dark' ? 'rgba(232, 232, 232, 0.9)' : 'rgba(0, 0, 0, 0.85)',
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif',
            fontWeight: 400,
            padding: [8, 4],
            backgroundColor: 'transparent',
            shadowBlur: 0,
            formatter: function(params) {
              // 为最右边节点的文本进行格式化
              const width = window.innerWidth;
              if (params.depth === 3) {
                // 对第四层节点（论文）进行特殊处理
                let name = params.name;

                // 直接强制添加换行 - 简单直接的方法
                // 将第一个空格替换为换行
                name = name.replace(/ /, '\n');

                // 对过长的标题进行缩写
                if (width <= 600) {
                  // 移动设备上更激进的缩写
                  name = name.replace('Metabolites & PHN', 'Metabolites');
                  name = name.replace('Multi-Omics & Weight', 'Multi-Omics');
                  name = name.replace('CKM Risk Stratification', 'CKM Risk');
                  name = name.replace('HP Detection & Gastritis', 'HP Det.');
                  name = name.replace('METS-VF & Chronic Pain', 'METS-VF');
                  name = name.replace('Stem Cell Therapy for OA', 'MSCs for OA');
                  name = name.replace('Genetic Epidemiology', 'Gen. Epi.');
                  // 处理作者名称
                  name = name.replace('Zhu et al.', 'Zhu');
                  name = name.replace('Yu & Zhu', 'Yu');
                  name = name.replace('He et al.', 'He');
                  name = name.replace('Lu & Zhu et al.', 'Lu & Zhu');
                  // 处理年份
                  name = name.replace('(2024)', '24');
                  name = name.replace('(2025)', '25');
                  name = name.replace('(2023)', '23');
                } else if (width <= 900) {
                  // 平板上的适度缩写
                  name = name.replace('Metabolites & PHN', 'Metabolites & PHN');
                  name = name.replace('Multi-Omics & Weight', 'Multi-Omics & Weight');
                  name = name.replace('METS-VF & Chronic Pain', 'METS-VF & Pain');
                  name = name.replace('HP Detection & Gastritis', 'HP Detection');
                  name = name.replace('Stem Cell Therapy for OA', 'MSCs for OA');
                }

                return name;
              }
              return params.name;
            }
          },
          draggable: false,
          left: '2%',
          right: isMobile ? '25%' : '15%',
          top: '2%',
          bottom: '2%',
          data: [
            // 第一层 - 主要研究领域
            { name: 'Precision Medicine', depth: 0, label: { fontSize: function() {
              const width = window.innerWidth;
              if (width <= 600) return 13;  // 移动设备
              if (width <= 900) return 14;  // 平板
              if (width <= 1200) return 15; // 小屏幕笔记本
              return 16; // 大屏幕
            }(), fontWeight: 500 }, itemStyle: { color: theme == 'dark' ? '#9370DB' : '#8A4FFF' } },

            // 第二层 - 具体研究方向
            { name: 'Bioinformatics', depth: 1, label: { fontSize: function() {
              const width = window.innerWidth;
              if (width <= 600) return 11;  // 移动设备
              if (width <= 900) return 12;  // 平板
              if (width <= 1200) return 13; // 小屏幕笔记本
              return 14; // 大屏幕
            }() }, itemStyle: { color: theme == 'dark' ? '#7CB9E8' : '#5F9EA0' } },
            { name: 'Genetic Epidemiology', depth: 1, label: { fontSize: function() {
              const width = window.innerWidth;
              if (width <= 600) return 11;  // 移动设备
              if (width <= 900) return 12;  // 平板
              if (width <= 1200) return 13; // 小屏幕笔记本
              return 14; // 大屏幕
            }() }, itemStyle: { color: theme == 'dark' ? '#B8860B' : '#DAA520' } },
            { name: 'Chronic Diseases', depth: 1, label: { fontSize: function() {
              const width = window.innerWidth;
              if (width <= 600) return 11;  // 移动设备
              if (width <= 900) return 12;  // 平板
              if (width <= 1200) return 13; // 小屏幕笔记本
              return 14; // 大屏幕
            }() }, itemStyle: { color: theme == 'dark' ? '#CD5C5C' : '#E9967A' } },
            { name: 'Metabolic Health', depth: 1, label: { fontSize: function() {
              const width = window.innerWidth;
              if (width <= 600) return 11;  // 移动设备
              if (width <= 900) return 12;  // 平板
              if (width <= 1200) return 13; // 小屏幕笔记本
              return 14; // 大屏幕
            }() }, itemStyle: { color: theme == 'dark' ? '#4682B4' : '#6495ED' } },

            // 第三层 - 研究方法/应用
            { name: 'Risk Prediction', depth: 2, label: { fontSize: function() {
              const width = window.innerWidth;
              if (width <= 600) return 10;  // 移动设备
              if (width <= 900) return 11;  // 平板
              if (width <= 1200) return 12; // 小屏幕笔记本
              return 13; // 大屏幕
            }() }, itemStyle: { color: theme == 'dark' ? '#D2691E' : '#F4A460' } },
            { name: 'Omics & Genetics', depth: 2, label: { fontSize: function() {
              const width = window.innerWidth;
              if (width <= 600) return 10;  // 移动设备
              if (width <= 900) return 11;  // 平板
              if (width <= 1200) return 12; // 小屏幕笔记本
              return 13; // 大屏幕
            }() }, itemStyle: { color: theme == 'dark' ? '#2E8B57' : '#66CDAA' } },
            { name: 'Disease Mechanisms', depth: 2, label: { fontSize: function() {
              const width = window.innerWidth;
              if (width <= 600) return 10;  // 移动设备
              if (width <= 900) return 11;  // 平板
              if (width <= 1200) return 12; // 小屏幕笔记本
              return 13; // 大屏幕
            }() }, itemStyle: { color: theme == 'dark' ? '#B22222' : '#CD5C5C' } },
            { name: 'Clinical Applications', depth: 2, label: { fontSize: function() {
              const width = window.innerWidth;
              if (width <= 600) return 10;  // 移动设备
              if (width <= 900) return 11;  // 平板
              if (width <= 1200) return 12; // 小屏幕笔记本
              return 13; // 大屏幕
            }() }, itemStyle: { color: theme == 'dark' ? '#4169E1' : '#1E90FF' } },

            // 第四层 - 具体研究成果
            { name: 'Zhu et al. (2024)\nMetabolites & PHN', depth: 3, label: { fontSize: function() {
              const width = window.innerWidth;
              if (width <= 600) return 9;   // 移动设备
              if (width <= 900) return 10;  // 平板
              if (width <= 1200) return 11; // 小屏幕笔记本
              return 12; // 大屏幕
            }(), fontWeight: 'lighter', align: 'left', position: 'right' }, itemStyle: { color: theme == 'dark' ? '#9370DB' : '#B19CD9' } },
            { name: 'Zhu et al. (2025)\nMETS-VF & Chronic Pain', depth: 3, label: { fontSize: function() {
              const width = window.innerWidth;
              if (width <= 600) return 9;   // 移动设备
              if (width <= 900) return 10;  // 平板
              if (width <= 1200) return 11; // 小屏幕笔记本
              return 12; // 大屏幕
            }(), fontWeight: 'lighter', align: 'left', position: 'right' }, itemStyle: { color: theme == 'dark' ? '#DB7093' : '#FFC0CB' } },
            { name: 'Zhu et al. (2025)\nCKM Risk Stratification', depth: 3, label: { fontSize: function() {
              const width = window.innerWidth;
              if (width <= 600) return 9;   // 移动设备
              if (width <= 900) return 10;  // 平板
              if (width <= 1200) return 11; // 小屏幕笔记本
              return 12; // 大屏幕
            }(), fontWeight: 'lighter', align: 'left', position: 'right' }, itemStyle: { color: theme == 'dark' ? '#DEB887' : '#FFDAB9' } },
            { name: 'Yu & Zhu (2025)\nMulti-Omics & Weight', depth: 3, label: { fontSize: function() {
              const width = window.innerWidth;
              if (width <= 600) return 9;   // 移动设备
              if (width <= 900) return 10;  // 平板
              if (width <= 1200) return 11; // 小屏幕笔记本
              return 12; // 大屏幕
            }(), fontWeight: 'lighter', align: 'left', position: 'right' }, itemStyle: { color: theme == 'dark' ? '#20B2AA' : '#AFEEEE' } },
            { name: 'He et al. (2023)\nHP Detection & Gastritis', depth: 3, label: { fontSize: function() {
              const width = window.innerWidth;
              if (width <= 600) return 9;   // 移动设备
              if (width <= 900) return 10;  // 平板
              if (width <= 1200) return 11; // 小屏幕笔记本
              return 12; // 大屏幕
            }(), fontWeight: 'lighter', align: 'left', position: 'right' }, itemStyle: { color: theme == 'dark' ? '#9932CC' : '#D8BFD8' } },
            { name: 'Lu & Zhu et al. (2025)\nStem Cell Therapy for OA', depth: 3, label: { fontSize: function() {
              const width = window.innerWidth;
              if (width <= 600) return 9;   // 移动设备
              if (width <= 900) return 10;  // 平板
              if (width <= 1200) return 11; // 小屏幕笔记本
              return 12; // 大屏幕
            }(), fontWeight: 'lighter', align: 'left', position: 'right' }, itemStyle: { color: theme == 'dark' ? '#4682B4' : '#87CEEB' } }
          ],
          links: [
            // 第一层到第二层的连接 - 总流量80，每个节点20
            { source: 'Precision Medicine', target: 'Bioinformatics', value: 20 },
            { source: 'Precision Medicine', target: 'Genetic Epidemiology', value: 20 },
            { source: 'Precision Medicine', target: 'Chronic Diseases', value: 20 },
            { source: 'Precision Medicine', target: 'Metabolic Health', value: 20 },

            // 第二层到第三层的连接 - 确保每个节点的入度等于出度
            { source: 'Bioinformatics', target: 'Risk Prediction', value: 10 },
            { source: 'Bioinformatics', target: 'Omics & Genetics', value: 10 },
            { source: 'Genetic Epidemiology', target: 'Risk Prediction', value: 10 },
            { source: 'Genetic Epidemiology', target: 'Omics & Genetics', value: 10 },
            { source: 'Chronic Diseases', target: 'Risk Prediction', value: 5 },
            { source: 'Chronic Diseases', target: 'Disease Mechanisms', value: 5 },
            { source: 'Chronic Diseases', target: 'Clinical Applications', value: 10 },
            { source: 'Metabolic Health', target: 'Risk Prediction', value: 15 },
            { source: 'Metabolic Health', target: 'Disease Mechanisms', value: 5 },

            // 第三层到第四层的连接 - 使每个节点的流量更加均衡
            { source: 'Risk Prediction', target: 'Zhu et al. (2024)\nMetabolites & PHN', value: 8 },
            { source: 'Risk Prediction', target: 'Zhu et al. (2025)\nMETS-VF & Chronic Pain', value: 10 },
            { source: 'Risk Prediction', target: 'Zhu et al. (2025)\nCKM Risk Stratification', value: 12 },
            { source: 'Risk Prediction', target: 'Yu & Zhu (2025)\nMulti-Omics & Weight', value: 10 },

            { source: 'Omics & Genetics', target: 'Zhu et al. (2024)\nMetabolites & PHN', value: 5 },
            { source: 'Omics & Genetics', target: 'Yu & Zhu (2025)\nMulti-Omics & Weight', value: 15 },

            { source: 'Disease Mechanisms', target: 'Zhu et al. (2024)\nMetabolites & PHN', value: 3 },
            { source: 'Disease Mechanisms', target: 'Zhu et al. (2025)\nMETS-VF & Chronic Pain', value: 7 },

            { source: 'Clinical Applications', target: 'He et al. (2023)\nHP Detection & Gastritis', value: 5 },
            { source: 'Clinical Applications', target: 'Lu & Zhu et al. (2025)\nStem Cell Therapy for OA', value: 5 }
          ],
          lineStyle: {
            color: 'gradient',
            curveness: 0.5, // 适中的曲率，使连接线更加自然
            opacity: theme == 'dark' ? 0.6 : 0.4, // 增加不透明度，使连接线更加明显
            shadowBlur: 2, // 添加轻微的阴影效果
            shadowColor: theme == 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.1)', // 阴影颜色
            shadowOffsetX: 0,
            shadowOffsetY: 0
          },
          itemStyle: {
            borderWidth: 0,
            borderRadius: 2, // 添加轻微的圆角
            shadowBlur: 2, // 添加轻微的阴影效果
            shadowColor: theme == 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.1)' // 阴影颜色
          }
        }
      };

      // 设置图表
      myChart.setOption(option);

      // 响应窗口大小变化
      window.addEventListener('resize', function() {
        myChart.resize();
      });

      // 监听主题变化
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.attributeName === 'class' &&
              mutation.target === document.documentElement) {
            updateEChartsTheme();
          }
        });
      });

      observer.observe(document.documentElement, { attributes: true });
    }
  });
</script>
