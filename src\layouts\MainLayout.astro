---
import '../styles/global.css';
import Header from '../components/common/Header.astro';
import Footer from '../components/common/Footer.astro';

interface Props {
  title: string;
  description?: string;
}

const { title, description = "朱健宇的个人网站" } = Astro.props;
---

<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content={description} />
    <meta name="author" content="Jianyu Zhu">
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <title>{title}</title>

    <!-- 仅加载 Playfair Display 字体用于名字部分 -->
    <link rel="preload" as="style" href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@600;700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@600;700&display=swap" media="print" onload="this.media='all'">
    <noscript>
      <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@600;700&display=swap">
    </noscript>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- ECharts 库 -->
    <script is:inline src="/scripts/echarts.min.js"></script>
  </head>
  <body class="bg-white dark:bg-[#121417] text-gray-800 dark:text-gray-100 pt-12 font-system transition-colors duration-300">
    <Header />

    <main class="container mx-auto px-4 pt-8 pb-8 max-w-[770px]">
      <slot />
    </main>

    <Footer />

    <script is:inline>
      // 确保外部链接在新标签页打开
      document.addEventListener('DOMContentLoaded', () => {
        document.querySelectorAll('a[href^="http"], a[href^="https"]').forEach(link => {
          if (!link.hasAttribute('target')) {
            link.setAttribute('target', '_blank');
          }
        });
      });
    </script>
  </body>
</html>
