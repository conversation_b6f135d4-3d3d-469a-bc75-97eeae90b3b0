---
import MainLayout from '../../layouts/MainLayout.astro';
---

<MainLayout title="Photography | Jianyu Zhu">
  <div class="post">
    <header class="post-header">
      <h1 class="text-4xl font-normal mb-6 text-gray-800 dark:text-gray-200">Photography</h1>
    </header>

    <article class="text-gray-800 dark:text-gray-200">
      <!-- 正在维护提示 -->
      <div class="flex flex-col items-center justify-center py-12 text-center">
        <div class="w-24 h-24 mb-8 relative">
          <i class="fa-solid fa-camera text-5xl text-blue-500 dark:text-blue-400"></i>
          <div class="absolute -top-2 -right-2 w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center animate-pulse">
            <i class="fa-solid fa-wrench text-white text-sm"></i>
          </div>
        </div>

        <h2 class="text-3xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Coming Soon</h2>
        <p class="text-xl mb-6 max-w-2xl text-gray-600 dark:text-gray-400">My photography portfolio is currently under construction. I'm working on curating a collection of my best work to share with you.</p>

        <div class="w-16 h-1 bg-blue-500 dark:bg-blue-400 mb-8"></div>

        <div class="bg-blue-50 dark:bg-blue-900/30 p-6 rounded-lg border border-blue-200 dark:border-blue-800 text-center max-w-xl mx-auto mb-8">
          <h3 class="text-xl font-medium text-blue-800 dark:text-blue-300 mb-3">Visit My 500px Portfolio</h3>
          <p class="text-gray-700 dark:text-gray-300 mb-4">While this page is being developed, you can view my photography work on 500px.</p>
          <a href="https://500px.com.cn/kinyuchu" target="_blank" class="inline-flex items-center px-5 py-2.5 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-300">
            <i class="fa-solid fa-camera mr-2"></i>
            View on 500px
          </a>
        </div>

        <!-- 进度条 -->
        <div class="w-full max-w-md mx-auto mb-8">
          <div class="flex justify-between mb-2">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Progress</span>
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">40%</span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
            <div class="bg-blue-600 dark:bg-blue-500 h-2.5 rounded-full" style="width: 40%"></div>
          </div>
        </div>

        <p class="text-gray-600 dark:text-gray-400 mb-6">Expected completion: June 2025</p>

        <a href="/" class="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-800/30 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-300">
          <i class="fa-solid fa-arrow-left mr-2"></i>
          Return to Home
        </a>
      </div>
    </article>
  </div>
</MainLayout>

<style>
  /* 动画效果 */
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
</style>
