@import "tailwindcss";

@custom-variant dark (&:where(.dark, .dark *));

:root {
  --global-bg-color: #fff;
  --global-text-color: #000;
  --global-text-color-light: #828282;
  --global-theme-color: #3b82f6;
  --global-hover-color: #2563eb;
  --global-hover-text-color: #fff;
  --global-footer-bg-color: #f8f9fa;
  --global-footer-text-color: #666;
  --global-footer-link-color: #404040;
  --global-divider-color: rgba(0, 0, 0, 0.1);
}

html[data-theme="dark"], .dark {
  --global-bg-color: #121417;
  --global-text-color: #e2e8f0;
  --global-text-color-light: #9ca3af;
  --global-theme-color: #3b82f6;
  --global-hover-color: #2563eb;
  --global-hover-text-color: #fff;
  --global-footer-bg-color: #18191d;
  --global-footer-text-color: #cbd5e0;
  --global-footer-link-color: #e2e8f0;
  --global-divider-color: #27292e;
}

/* 字体设置 */
@layer base {
  html {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    font-size: 16px;
    line-height: 1.5;
    transition: background-color 0.3s ease, color 0.3s ease;
  }
  h1, h2, h3, h4, h5, h6 {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
    line-height: 1.25;
    font-weight: 600;
  }

  h1 {
    font-size: 2.5rem;
    letter-spacing: -0.5px;
  }

  h2 {
    font-size: 2rem;
    letter-spacing: -0.3px;
  }

  h3 {
    font-size: 1.75rem;
  }

  h4 {
    font-size: 1.3rem;
    letter-spacing: 0.01em;
    font-weight: 700;
    color: #374151;
    text-shadow: 0.5px 0.5px 0px rgba(0, 0, 0, 0.05);
    margin-bottom: 0.8rem;
    display: inline-block;
  }

  p {
    margin-bottom: 1rem;
    text-align: justify;
  }

  @media (max-width: 640px) {
    html {
      font-size: 15px;
    }

    h1 {
      font-size: 2rem;
    }

    h2 {
      font-size: 1.75rem;
    }

    h3 {
      font-size: 1.5rem;
    }

    h4 {
      font-size: 1.2rem;
      letter-spacing: 0.01em;
      margin-bottom: 0.7rem;
    }
  }
}

/* 自定义工具类 */
.font-system {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}

.font-heading {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
}

.font-weight-light {
  font-weight: 300;
}

.font-weight-normal {
  font-weight: 400;
}

.font-weight-medium {
  font-weight: 500;
}

.font-weight-semibold {
  font-weight: 600;
}

.font-weight-bold {
  font-weight: 700;
}

/* 标题默认字重 */
h1 {
  font-weight: 600;
}

h2 {
  font-weight: 600;
}

h3, h4, h5, h6 {
  font-weight: 600;
}

/* 系统字体特性增强 */
.font-heading {
  font-feature-settings: "liga" 1, "lnum" 1, "pnum" 1, "kern" 1;
  letter-spacing: -0.01em;
}

/* Playfair Display字体特性增强 */
.font-playfair {
  font-family: 'Playfair Display', serif;
  font-feature-settings: "liga" 1, "lnum" 1, "pnum" 1, "kern" 1;
  letter-spacing: -0.02em;
  font-weight: 600;
  text-rendering: optimizeLegibility;
}

/* 链接和交互元素样式 */
a {
  color: var(--global-theme-color);
  text-decoration: none;
  transition: all 0.3s ease;
  border-bottom: 1px solid transparent;
  font-weight: 500;
}

a:hover {
  color: var(--global-hover-color);
  border-bottom-color: var(--global-hover-color);
}

/* 标题中的链接 */
h1 a, h2 a, h3 a, h4 a, h5 a, h6 a {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
  font-weight: inherit;
}

/* 列表样式增强 */
ul.list-disc li {
  margin-bottom: 0.4rem;
  line-height: 1.4;
  padding-left: 0.25rem;
}

/* 段落间距 */
p + p {
  margin-top: 0.5rem;
}

/* 暗黑模式下的特定样式 */
.dark h4 {
  color: #e5e7eb;
  text-shadow: 0.5px 0.5px 0px rgba(0, 0, 0, 0.2);
}

.dark a {
  color: var(--global-theme-color);
}

.dark a:hover {
  color: var(--global-hover-color);
}

/* 暗黑模式下的阴影效果 */
.dark .shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

/* 暗黑模式下的卡片效果 */
.dark .bg-white {
  background-color: #18191d;
}

/* 暗黑模式下的边框颜色 */
.dark .border-gray-200 {
  border-color: #27292e;
}
